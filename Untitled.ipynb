{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f4f1eaab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports done\n"]}], "source": ["#imports\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional\n", "from tensorflow.keras.preprocessing.text import Tokenizer\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras import models\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras import regularizers\n", "import tensorflow.keras.utils as ku \n", "import numpy as np\n", "print(\"Imports done\") #debug"]}, {"cell_type": "code", "execution_count": 2, "id": "e2c3c409", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["opening done\n", "corpus split\n", "tokenizer complete\n", "total words 2969\n"]}], "source": ["#pre processing\n", "tokenizer = Tokenizer()\n", "data = open('../../../../../Cooleg Extra/CSE4022/stories.txt', encoding='utf-8').read() #to fix\n", "print(\"opening done\") #debug\n", "corpus = data.lower().split(\"\\n\")\n", "print(\"corpus split\") #debug\n", "tokenizer.fit_on_texts(corpus)\n", "print(\"tokenizer complete\") #debug\n", "total_words = len(tokenizer.word_index) + 1\n", "print(\"total words\", total_words) #debug"]}, {"cell_type": "code", "execution_count": null, "id": "5dd6449b", "metadata": {}, "outputs": [], "source": ["print(corpus[0])"]}, {"cell_type": "code", "execution_count": null, "id": "a79a3593", "metadata": {}, "outputs": [], "source": ["import json\n", "json_object = tokenizer.to_json()\n", "config = json.loads(json_object)\n", "l = list(dict(eval(config['config']['word_counts'])).keys())\n", "\n", "with open(\"vocabulary.txt\", \"w\") as outfile:\n", "    for i in l:\n", "        outfile.write(i + \"\\n\") "]}, {"cell_type": "code", "execution_count": 3, "id": "5c638aad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input sequences generated\n", "88\n", "input sequences padded\n", "predictors and label created\n"]}], "source": ["#creating input sequences\n", "input_sequences = []\n", "for line in corpus:\n", "    token_list = tokenizer.texts_to_sequences([line])[0]\n", "    for i in range(1, len(token_list)):\n", "        n_gram_sequence = token_list[:i+1]\n", "        input_sequences.append(n_gram_sequence)\n", "print(\"input sequences generated\") #debug\n", "# pad sequences \n", "max_sequence_len = max([len(x) for x in input_sequences])\n", "print(max_sequence_len)\n", "input_sequences = np.array(pad_sequences(input_sequences, maxlen=max_sequence_len, padding='pre'))\n", "print(\"input sequences padded\") #debug\n", "# create predictors and label\n", "predictors, label = input_sequences[:,:-1],input_sequences[:,-1]\n", "\n", "label = ku.to_categorical(label, num_classes=total_words)\n", "print(\"predictors and label created\")#debug"]}, {"cell_type": "code", "execution_count": null, "id": "e849d5ac", "metadata": {}, "outputs": [], "source": ["#creating a sequential model from scratch\n", "\n", "'''model = Sequential()\n", "model.add(Embedding(total_words, 100, input_length=max_sequence_len-1))\n", "model.add(Bidirectional(LSTM(150, return_sequences = True)))\n", "model.add(Dropout(0.2))\n", "model.add(LSTM(100))\n", "model.add(Den<PERSON>(total_words/2, activation='relu', kernel_regularizer=regularizers.l2(0.01)))\n", "model.add(Dense(total_words, activation='softmax'))\n", "model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])\n", "print(model.summary())\n", "'''\n", "\n", "#loading presaved model\n", "new_model = models.load_model('saved_model/mainmodel')\n", "#training for x epochs\n", "\n", "history = new_model.fit(predictors, label, epochs=1, verbose=1)\n", "\n", "#saving the model for future use\n", "new_model.save('saved_model/mainmodel')\n", "#plot accuracy and loss\n", "import matplotlib.pyplot as plt\n", "acc = history.history['accuracy']\n", "loss = history.history['loss']\n", "epochs = range(len(acc))\n", "plt.plot(epochs, acc, 'b', label='Training accuracy')\n", "plt.title('Training accuracy')\n", "plt.figure()\n", "plt.plot(epochs, loss, 'b', label='Training Loss')\n", "plt.title('Training loss')\n", "plt.legend()\n", "plt.show()\n", "print(history.history['accuracy'])\n", "print(history.history['loss'])"]}, {"cell_type": "code", "execution_count": null, "id": "2a9e6876", "metadata": {"scrolled": false}, "outputs": [], "source": ["#testing\n", "new_model = models.load_model('saved_model/mainmodel')\n", "seed_text = input(\"Input String: \")\n", "next_words = int(input(\"Number of words: \"))\n", "for _ in range(next_words):\n", "    token_list = tokenizer.texts_to_sequences([seed_text])[0]\n", "    token_list = pad_sequences([token_list], maxlen=max_sequence_len-1, padding='pre')\n", "    #predicted = model.predict_classes(token_list, verbose=0)\n", "    predicted = new_model.predict(token_list)\n", "    #predicted = new_model(token_list)\n", "    output_word = \"\"\n", "    for word, index in tokenizer.word_index.items():\n", "        if index == np.argmax(predicted):\n", "            output_word = word\n", "            break\n", "    seed_text += \" \" + output_word\n", "print(seed_text)"]}, {"cell_type": "code", "execution_count": null, "id": "8d092fbd", "metadata": {"scrolled": true}, "outputs": [], "source": ["#saving the model for future use\n", "new_model.save('saved_model/mainmodel')"]}, {"cell_type": "code", "execution_count": null, "id": "cce36297", "metadata": {"scrolled": false}, "outputs": [], "source": ["new_model = models.load_model('saved_model/mainmodel')\n", "print(new_model.summary())"]}, {"cell_type": "code", "execution_count": null, "id": "ca356d7e", "metadata": {}, "outputs": [], "source": ["import random\n", "from nltk.translate.bleu_score import SmoothingFunction, corpus_bleu, sentence_bleu\n", "\n", "new_model = models.load_model('saved_model/mainmodel')\n", "def generate(text, size):\n", "    for _ in range(size):\n", "        token_list = tokenizer.texts_to_sequences([text])[0]\n", "        token_list = pad_sequences([token_list], maxlen=87, padding='pre')\n", "        predicted = new_model.predict(token_list)\n", "        output_word = \"\"\n", "        for word, index in tokenizer.word_index.items():\n", "            if index == np.argmax(predicted):\n", "                output_word = word\n", "                break\n", "        text += \" \" + output_word\n", "    return text\n", "\n", "ref = []\n", "gen = []\n", "skipped = 0\n", "i = 0\n", "with open(\"D:\\Documents\\Cooleg Extra\\CSE4022\\other_dataset_files\\dataset_wikipedia.txt\", 'r') as file:\n", "    lines = file.readlines()\n", "while i < 50:\n", "    inp1 = lines[random.randint(0, len(lines)-1)]\n", "    inp2 = inp1.split()\n", "    if(len(inp2) < 2 or len(inp2) > 86):\n", "        skipped += 1\n", "        continue\n", "    ref.append(inp1)\n", "    a = len(inp2)\n", "    r = random.randint(1,len(inp2)-1)\n", "    inp2 = inp2[:r]\n", "    gen.append(generate(' '.join(inp2), a-r))\n", "    i += 1\n", "\n", "    \n", "score = 0\n", "ref_bleu = []\n", "gen_bleu = []\n", "for l in gen:\n", "    gen_bleu.append(l.split())\n", "for i,l in enumerate(ref):\n", "    ref_bleu.append([l.split()])\n", "cc = SmoothingFunction()\n", "score += corpus_bleu(ref_bleu, gen_bleu, weights=(0, 1, 0, 0), smoothing_function=cc.method4)\n", "print(score, skipped)\n"]}, {"cell_type": "code", "execution_count": null, "id": "8c9412d4", "metadata": {}, "outputs": [], "source": ["print(ref[1])\n", "print(gen[1])"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.9 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}, "vscode": {"interpreter": {"hash": "bba799b22eac475ea7c3b93ab2f6dabb5a4063ae89b5a0aa20ba2855c3f4298b"}}}, "nbformat": 4, "nbformat_minor": 5}