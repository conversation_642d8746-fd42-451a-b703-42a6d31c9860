"""
Data preprocessing module for Next Word Prediction using TinyStories dataset.
This module handles dataset downloading, text preprocessing, and sequence generation.
"""

import os
import re
import numpy as np
import pandas as pd
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.model_selection import train_test_split
import requests
import zipfile
from io import BytesIO


class DataPreprocessor:
    """
    A class to handle data preprocessing for next word prediction model.
    """
    
    def __init__(self, max_sequence_length=50, vocab_size=10000):
        """
        Initialize the preprocessor.
        
        Args:
            max_sequence_length (int): Maximum length of input sequences
            vocab_size (int): Maximum vocabulary size
        """
        self.max_sequence_length = max_sequence_length
        self.vocab_size = vocab_size
        self.tokenizer = None
        self.sequences = []
        self.total_words = 0
        
    def download_tinystories_dataset(self, data_dir="data"):
        """
        Download TinyStories dataset from Kaggle.
        Note: This is a placeholder. You'll need to manually download from <PERSON><PERSON>
        and place the files in the data directory.
        
        Args:
            data_dir (str): Directory to save the dataset
        """
        os.makedirs(data_dir, exist_ok=True)
        
        print("Please download the TinyStories dataset manually from:")
        print("https://www.kaggle.com/datasets/thedevastator/tinystories-narrative-classification")
        print("or")
        print("https://www.kaggle.com/datasets/tomasbebra/tiny-stories-ds")
        print(f"Place the CSV files in the '{data_dir}' directory")
        
        # Check if data files exist
        possible_files = ['train.csv', 'stories.csv', 'tinystories.csv']
        for file in possible_files:
            if os.path.exists(os.path.join(data_dir, file)):
                print(f"Found dataset file: {file}")
                return os.path.join(data_dir, file)
        
        print("No dataset files found. Please download manually.")
        return None
    
    def load_sample_data(self, data_dir="data"):
        """
        Create a sample dataset for testing if no real dataset is available.
        
        Args:
            data_dir (str): Directory to save the sample data
        """
        sample_stories = [
            "Once upon a time there was a little girl named Lucy who loved to play in the garden.",
            "The cat sat on the mat and watched the birds fly in the sky.",
            "Tommy went to the store with his mother to buy some apples and oranges.",
            "The dog ran quickly through the park chasing a red ball.",
            "Sarah found a beautiful flower in the forest and picked it carefully.",
            "The sun was shining brightly when the children went outside to play.",
            "A small mouse lived in a hole under the big oak tree.",
            "The teacher read a story about dragons and princesses to the class.",
            "Jack climbed the tall mountain to see the view from the top.",
            "The baby elephant played with water at the river with its family.",
            "Emma baked cookies with her grandmother on a rainy afternoon.",
            "The bird built a nest in the tree using twigs and leaves.",
            "Peter rode his bicycle to school every morning before class started.",
            "The fish swam happily in the clear blue water of the pond.",
            "Anna drew pictures of flowers and butterflies in her notebook.",
            "The rabbit hopped through the meadow looking for fresh carrots.",
            "The old man told stories about his adventures when he was young.",
            "The children played hide and seek in the backyard until dinner time.",
            "The butterfly landed gently on the colorful flowers in the garden.",
            "The train traveled through the countryside carrying many passengers."
        ]
        
        os.makedirs(data_dir, exist_ok=True)
        sample_df = pd.DataFrame({'text': sample_stories})
        sample_path = os.path.join(data_dir, 'sample_stories.csv')
        sample_df.to_csv(sample_path, index=False)
        print(f"Created sample dataset at: {sample_path}")
        return sample_path
    
    def clean_text(self, text):
        """
        Clean and preprocess text data.
        
        Args:
            text (str): Raw text to clean
            
        Returns:
            str: Cleaned text
        """
        # Convert to lowercase
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^a-zA-Z0-9\s\.\,\!\?\;\:]', '', text)
        
        # Remove multiple punctuation
        text = re.sub(r'[\.]{2,}', '.', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        return text.strip()
    
    def load_and_preprocess_data(self, file_path=None):
        """
        Load and preprocess the dataset.
        
        Args:
            file_path (str): Path to the dataset file
            
        Returns:
            list: List of cleaned text data
        """
        if file_path is None:
            file_path = self.load_sample_data()
        
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            file_path = self.load_sample_data()
        
        # Load data
        try:
            df = pd.read_csv(file_path)
            # Try different column names
            text_column = None
            for col in ['text', 'story', 'content', 'narrative']:
                if col in df.columns:
                    text_column = col
                    break
            
            if text_column is None:
                text_column = df.columns[0]  # Use first column
                
            texts = df[text_column].dropna().tolist()
        except Exception as e:
            print(f"Error loading CSV: {e}")
            print("Using sample data instead...")
            file_path = self.load_sample_data()
            df = pd.read_csv(file_path)
            texts = df['text'].tolist()
        
        # Clean texts
        cleaned_texts = [self.clean_text(text) for text in texts]
        
        print(f"Loaded {len(cleaned_texts)} stories")
        print(f"Sample text: {cleaned_texts[0][:100]}...")
        
        return cleaned_texts
    
    def create_sequences(self, texts):
        """
        Create input-output sequences for training.
        
        Args:
            texts (list): List of cleaned texts
            
        Returns:
            tuple: (input_sequences, labels, tokenizer)
        """
        # Combine all texts
        corpus = ' '.join(texts)
        
        # Initialize tokenizer
        self.tokenizer = Tokenizer(num_words=self.vocab_size, oov_token='<OOV>')
        self.tokenizer.fit_on_texts([corpus])
        
        self.total_words = len(self.tokenizer.word_index) + 1
        print(f"Total unique words: {self.total_words}")
        
        # Create sequences
        input_sequences = []
        
        for text in texts:
            token_list = self.tokenizer.texts_to_sequences([text])[0]
            
            # Create n-gram sequences
            for i in range(1, len(token_list)):
                n_gram_sequence = token_list[:i+1]
                input_sequences.append(n_gram_sequence)
        
        # Pad sequences
        max_sequence_len = max([len(seq) for seq in input_sequences])
        self.max_sequence_length = min(max_sequence_len, self.max_sequence_length)
        
        input_sequences = pad_sequences(input_sequences, 
                                      maxlen=self.max_sequence_length, 
                                      padding='pre')
        
        # Create input and labels
        X = input_sequences[:, :-1]
        y = input_sequences[:, -1]
        
        print(f"Created {len(X)} sequences")
        print(f"Sequence length: {X.shape[1]}")
        print(f"Vocabulary size: {self.total_words}")
        
        return X, y
    
    def prepare_data(self, file_path=None, test_size=0.2, random_state=42):
        """
        Complete data preparation pipeline.
        
        Args:
            file_path (str): Path to dataset file
            test_size (float): Proportion of test data
            random_state (int): Random seed
            
        Returns:
            tuple: (X_train, X_test, y_train, y_test)
        """
        # Load and preprocess data
        texts = self.load_and_preprocess_data(file_path)
        
        # Create sequences
        X, y = self.create_sequences(texts)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        print(f"Training data: {X_train.shape}")
        print(f"Test data: {X_test.shape}")
        
        return X_train, X_test, y_train, y_test
    
    def save_tokenizer(self, filepath="models/tokenizer.json"):
        """
        Save the tokenizer for later use.
        
        Args:
            filepath (str): Path to save the tokenizer
        """
        if self.tokenizer is None:
            print("No tokenizer to save. Please run prepare_data first.")
            return
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Save tokenizer configuration
        tokenizer_json = self.tokenizer.to_json()
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(tokenizer_json)
        
        print(f"Tokenizer saved to: {filepath}")
    
    def load_tokenizer(self, filepath="models/tokenizer.json"):
        """
        Load a saved tokenizer.
        
        Args:
            filepath (str): Path to the saved tokenizer
        """
        if not os.path.exists(filepath):
            print(f"Tokenizer file not found: {filepath}")
            return False
        
        with open(filepath, 'r', encoding='utf-8') as f:
            tokenizer_json = f.read()
        
        self.tokenizer = Tokenizer()
        self.tokenizer = self.tokenizer.from_json(tokenizer_json)
        self.total_words = len(self.tokenizer.word_index) + 1
        
        print(f"Tokenizer loaded from: {filepath}")
        return True


if __name__ == "__main__":
    # Example usage
    preprocessor = DataPreprocessor(max_sequence_length=30, vocab_size=5000)
    
    # Prepare data
    X_train, X_test, y_train, y_test = preprocessor.prepare_data()
    
    # Save tokenizer
    preprocessor.save_tokenizer()
    
    print("Data preprocessing completed!")
