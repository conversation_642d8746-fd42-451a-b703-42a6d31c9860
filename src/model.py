"""
Bidirectional LSTM model for Next Word Prediction.
This module contains the model architecture and related utilities.
"""

import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import (
    Embedding, Bidirectional, LSTM, Dense, Dropout, 
    BatchNormalization, GlobalMaxPooling1D
)
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import (
    EarlyStopping, ModelCheckpoint, ReduceLROnPlateau, 
    TensorBoard
)
from tensorflow.keras.regularizers import l2
import os
import numpy as np


class BiLSTMNextWordPredictor:
    """
    Bidirectional LSTM model for next word prediction.
    """
    
    def __init__(self, vocab_size, embedding_dim=100, lstm_units=128, 
                 sequence_length=30, dropout_rate=0.3):
        """
        Initialize the model.
        
        Args:
            vocab_size (int): Size of vocabulary
            embedding_dim (int): Dimension of word embeddings
            lstm_units (int): Number of LSTM units
            sequence_length (int): Length of input sequences
            dropout_rate (float): Dropout rate for regularization
        """
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.lstm_units = lstm_units
        self.sequence_length = sequence_length
        self.dropout_rate = dropout_rate
        self.model = None
        self.history = None
        
    def build_model(self):
        """
        Build the Bidirectional LSTM model architecture.
        
        Returns:
            tf.keras.Model: Compiled model
        """
        model = Sequential([
            # Embedding layer
            Embedding(
                input_dim=self.vocab_size,
                output_dim=self.embedding_dim,
                input_length=self.sequence_length - 1,
                mask_zero=True,
                name='embedding'
            ),
            
            # First Bidirectional LSTM layer
            Bidirectional(
                LSTM(
                    self.lstm_units,
                    return_sequences=True,
                    dropout=self.dropout_rate,
                    recurrent_dropout=self.dropout_rate,
                    kernel_regularizer=l2(0.01),
                    recurrent_regularizer=l2(0.01)
                ),
                name='bidirectional_lstm_1'
            ),
            
            # Batch normalization
            BatchNormalization(name='batch_norm_1'),
            
            # Second Bidirectional LSTM layer
            Bidirectional(
                LSTM(
                    self.lstm_units // 2,
                    return_sequences=True,
                    dropout=self.dropout_rate,
                    recurrent_dropout=self.dropout_rate,
                    kernel_regularizer=l2(0.01),
                    recurrent_regularizer=l2(0.01)
                ),
                name='bidirectional_lstm_2'
            ),
            
            # Batch normalization
            BatchNormalization(name='batch_norm_2'),
            
            # Global max pooling to get fixed-size output
            GlobalMaxPooling1D(name='global_max_pooling'),
            
            # Dense layers with dropout
            Dense(
                self.lstm_units,
                activation='relu',
                kernel_regularizer=l2(0.01),
                name='dense_1'
            ),
            Dropout(self.dropout_rate, name='dropout_1'),
            
            Dense(
                self.lstm_units // 2,
                activation='relu',
                kernel_regularizer=l2(0.01),
                name='dense_2'
            ),
            Dropout(self.dropout_rate, name='dropout_2'),
            
            # Output layer
            Dense(
                self.vocab_size,
                activation='softmax',
                name='output'
            )
        ])
        
        # Compile model
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        return model
    
    def get_model_summary(self):
        """
        Get model summary.
        
        Returns:
            str: Model summary
        """
        if self.model is None:
            self.build_model()
        
        return self.model.summary()
    
    def create_callbacks(self, model_save_path="models/best_model.h5", 
                        log_dir="logs"):
        """
        Create training callbacks.
        
        Args:
            model_save_path (str): Path to save best model
            log_dir (str): Directory for TensorBoard logs
            
        Returns:
            list: List of callbacks
        """
        os.makedirs(os.path.dirname(model_save_path), exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        callbacks = [
            # Early stopping
            EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),
            
            # Model checkpoint
            ModelCheckpoint(
                filepath=model_save_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),
            
            # Reduce learning rate on plateau
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),
            
            # TensorBoard logging
            TensorBoard(
                log_dir=log_dir,
                histogram_freq=1,
                write_graph=True,
                write_images=True
            )
        ]
        
        return callbacks
    
    def train(self, X_train, y_train, X_val, y_val, epochs=100, 
              batch_size=64, model_save_path="models/best_model.h5"):
        """
        Train the model.
        
        Args:
            X_train (np.array): Training input data
            y_train (np.array): Training target data
            X_val (np.array): Validation input data
            y_val (np.array): Validation target data
            epochs (int): Number of training epochs
            batch_size (int): Batch size for training
            model_save_path (str): Path to save the best model
            
        Returns:
            tf.keras.callbacks.History: Training history
        """
        if self.model is None:
            self.build_model()
        
        print("Model Architecture:")
        self.model.summary()
        
        # Create callbacks
        callbacks = self.create_callbacks(model_save_path)
        
        # Train model
        print(f"\nStarting training for {epochs} epochs...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        return self.history
    
    def evaluate(self, X_test, y_test):
        """
        Evaluate the model on test data.
        
        Args:
            X_test (np.array): Test input data
            y_test (np.array): Test target data
            
        Returns:
            dict: Evaluation metrics
        """
        if self.model is None:
            print("Model not trained yet!")
            return None
        
        # Evaluate model
        test_loss, test_accuracy = self.model.evaluate(X_test, y_test, verbose=0)
        
        # Calculate perplexity
        perplexity = np.exp(test_loss)
        
        metrics = {
            'test_loss': test_loss,
            'test_accuracy': test_accuracy,
            'perplexity': perplexity
        }
        
        print(f"Test Loss: {test_loss:.4f}")
        print(f"Test Accuracy: {test_accuracy:.4f}")
        print(f"Perplexity: {perplexity:.4f}")
        
        return metrics
    
    def predict_next_word(self, tokenizer, seed_text, num_words=1, temperature=1.0):
        """
        Predict next word(s) given a seed text.
        
        Args:
            tokenizer: Fitted tokenizer
            seed_text (str): Input text
            num_words (int): Number of words to predict
            temperature (float): Sampling temperature for diversity
            
        Returns:
            str: Generated text
        """
        if self.model is None:
            print("Model not trained yet!")
            return seed_text
        
        generated_text = seed_text
        
        for _ in range(num_words):
            # Tokenize input
            token_list = tokenizer.texts_to_sequences([generated_text])[0]
            
            # Pad sequence
            token_list = tf.keras.preprocessing.sequence.pad_sequences(
                [token_list], 
                maxlen=self.sequence_length - 1, 
                padding='pre'
            )
            
            # Predict
            predictions = self.model.predict(token_list, verbose=0)[0]
            
            # Apply temperature
            predictions = predictions / temperature
            predictions = tf.nn.softmax(predictions).numpy()
            
            # Sample from predictions
            predicted_id = np.random.choice(len(predictions), p=predictions)
            
            # Convert back to word
            output_word = ""
            for word, index in tokenizer.word_index.items():
                if index == predicted_id:
                    output_word = word
                    break
            
            if output_word:
                generated_text += " " + output_word
        
        return generated_text
    
    def save_model(self, filepath="models/bilstm_model.h5"):
        """
        Save the trained model.
        
        Args:
            filepath (str): Path to save the model
        """
        if self.model is None:
            print("No model to save!")
            return
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        self.model.save(filepath)
        print(f"Model saved to: {filepath}")
    
    def load_model(self, filepath="models/bilstm_model.h5"):
        """
        Load a saved model.
        
        Args:
            filepath (str): Path to the saved model
            
        Returns:
            bool: Success status
        """
        if not os.path.exists(filepath):
            print(f"Model file not found: {filepath}")
            return False
        
        self.model = tf.keras.models.load_model(filepath)
        print(f"Model loaded from: {filepath}")
        return True


def create_simple_model(vocab_size, sequence_length):
    """
    Create a simpler Bi-LSTM model for quick testing.
    
    Args:
        vocab_size (int): Size of vocabulary
        sequence_length (int): Length of input sequences
        
    Returns:
        tf.keras.Model: Compiled model
    """
    model = Sequential([
        Embedding(vocab_size, 50, input_length=sequence_length - 1),
        Bidirectional(LSTM(64, return_sequences=True)),
        Bidirectional(LSTM(32)),
        Dense(vocab_size, activation='softmax')
    ])
    
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model


if __name__ == "__main__":
    # Example usage
    print("Creating Bi-LSTM model...")
    
    # Model parameters
    vocab_size = 5000
    sequence_length = 30
    
    # Create model
    predictor = BiLSTMNextWordPredictor(
        vocab_size=vocab_size,
        sequence_length=sequence_length,
        embedding_dim=100,
        lstm_units=128
    )
    
    # Build and show model
    model = predictor.build_model()
    predictor.get_model_summary()
    
    print("Model created successfully!")
