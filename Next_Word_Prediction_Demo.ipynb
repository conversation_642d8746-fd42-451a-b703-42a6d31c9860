{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Next Word Prediction with Bidirectional LSTM\n", "\n", "This notebook demonstrates how to train and use a Bidirectional LSTM model for next word prediction."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install requirements if running on Kaggle or Colab\n", "# !pip install tensorflow matplotlib seaborn\n", "\n", "import os\n", "import sys\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Add src to path\n", "sys.path.append('src')\n", "\n", "from src.data_preprocessing import DataPreprocessor\n", "from src.model import BiLSTMNextWordPredictor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Preprocessing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize preprocessor\n", "preprocessor = DataPreprocessor(\n", "    max_sequence_length=30,\n", "    vocab_size=5000\n", ")\n", "\n", "# Prepare data\n", "# If you have downloaded TinyStories dataset, specify the path:\n", "# data_path = 'data/stories.csv'  # Update this path\n", "data_path = None  # Will use sample data\n", "\n", "X_train, X_test, y_train, y_test = preprocessor.prepare_data(data_path)\n", "\n", "print(f\"Training data shape: {X_train.shape}\")\n", "print(f\"Test data shape: {X_test.shape}\")\n", "print(f\"Vocabulary size: {preprocessor.total_words}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Model Creation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create model\n", "model = BiLSTMNextWordPredictor(\n", "    vocab_size=preprocessor.total_words,\n", "    embedding_dim=100,\n", "    lstm_units=128,\n", "    sequence_length=preprocessor.max_sequence_length,\n", "    dropout_rate=0.3\n", ")\n", "\n", "# Build model\n", "keras_model = model.build_model()\n", "model.get_model_summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train model\n", "# Adjust epochs based on your needs (use more for better results)\n", "epochs = 20  # Increase this for better results\n", "batch_size = 64\n", "\n", "history = model.train(\n", "    X_train, y_train, \n", "    X_test, y_test,\n", "    epochs=epochs,\n", "    batch_size=batch_size,\n", "    model_save_path='models/notebook_model.h5'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Training Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Loss plot\n", "ax1.plot(history.history['loss'], label='Training Loss')\n", "ax1.plot(history.history['val_loss'], label='Validation Loss')\n", "ax1.set_title('Model Loss')\n", "ax1.set_xlabel('Epoch')\n", "ax1.set_ylabel('Loss')\n", "ax1.legend()\n", "ax1.grid(True)\n", "\n", "# Accuracy plot\n", "ax2.plot(history.history['accuracy'], label='Training Accuracy')\n", "ax2.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "ax2.set_title('Model Accuracy')\n", "ax2.set_xlabel('Epoch')\n", "ax2.set_ylabel('Accuracy')\n", "ax2.legend()\n", "ax2.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Model Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate model\n", "metrics = model.evaluate(X_test, y_test)\n", "\n", "print(\"\\nEvaluation Results:\")\n", "for metric, value in metrics.items():\n", "    print(f\"{metric}: {value:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Text Generation Examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test predictions\n", "test_sentences = [\n", "    \"once upon a time\",\n", "    \"the little girl\",\n", "    \"in the forest\",\n", "    \"the cat sat\",\n", "    \"it was a beautiful\"\n", "]\n", "\n", "print(\"Text Generation Examples:\")\n", "print(\"=\" * 50)\n", "\n", "for sentence in test_sentences:\n", "    # Conservative prediction\n", "    conservative = model.predict_next_word(\n", "        preprocessor.tokenizer, \n", "        sentence, \n", "        num_words=4,\n", "        temperature=0.5\n", "    )\n", "    \n", "    # Balanced prediction\n", "    balanced = model.predict_next_word(\n", "        preprocessor.tokenizer, \n", "        sentence, \n", "        num_words=4,\n", "        temperature=1.0\n", "    )\n", "    \n", "    # Creative prediction\n", "    creative = model.predict_next_word(\n", "        preprocessor.tokenizer, \n", "        sentence, \n", "        num_words=4,\n", "        temperature=1.5\n", "    )\n", "    \n", "    print(f\"\\nInput: '{sentence}'\")\n", "    print(f\"Conservative: {conservative}\")\n", "    print(f\"Balanced:     {balanced}\")\n", "    print(f\"Creative:     {creative}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Interactive Prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive prediction function\n", "def predict_text(input_text, num_words=5, temperature=1.0):\n", "    \"\"\"Generate text prediction.\"\"\"\n", "    result = model.predict_next_word(\n", "        preprocessor.tokenizer,\n", "        input_text,\n", "        num_words=num_words,\n", "        temperature=temperature\n", "    )\n", "    return result\n", "\n", "# Example usage\n", "input_text = \"the magical forest was\"  # Change this to test different inputs\n", "prediction = predict_text(input_text, num_words=6, temperature=1.0)\n", "\n", "print(f\"Input: {input_text}\")\n", "print(f\"Prediction: {prediction}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Save Model and Tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save model and tokenizer for later use\n", "model.save_model('models/notebook_bilstm_model.h5')\n", "preprocessor.save_tokenizer('models/notebook_tokenizer.json')\n", "\n", "print(\"Model and tokenizer saved successfully!\")\n", "print(\"You can now use predict.py script for inference.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Next Steps\n", "\n", "To improve the model:\n", "\n", "1. **Use a larger dataset**: Download the TinyStories dataset from Kaggle\n", "2. **Increase training epochs**: Train for 50-100 epochs for better results\n", "3. **Experiment with hyperparameters**: Try different embedding dimensions, LSTM units, etc.\n", "4. **Add more data preprocessing**: Clean text more thoroughly\n", "5. **Use pre-trained embeddings**: Consider using GloVe or Word2Vec embeddings\n", "\n", "For production use:\n", "- Use the `train.py` script for full training pipeline\n", "- Use the `predict.py` script for inference\n", "- Monitor training with TensorBoard logs"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}