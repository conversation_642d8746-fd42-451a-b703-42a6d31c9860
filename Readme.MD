# Next Word Prediction with Bidirectional LSTM

This project implements a next word prediction model using Bidirectional LSTM (Long Short-Term Memory) networks with TensorFlow Keras. The model is designed to predict the next word in a sequence given a context of previous words.

## Features

- **Bidirectional LSTM Architecture**: Uses both forward and backward LSTM layers for better context understanding
- **TinyStories Dataset Support**: Designed to work with the TinyStories dataset from Kaggle
- **Comprehensive Evaluation**: Includes accuracy, perplexity, and top-k accuracy metrics
- **Flexible Prediction**: Supports various sampling strategies (temperature, top-k, top-p)
- **Interactive Mode**: Command-line interface for real-time predictions
- **Training Visualization**: Automatic generation of training plots and metrics

## Project Structure

```
CSE4022_Project/
├── src/
│   ├── data_preprocessing.py    # Data loading and preprocessing
│   └── model.py                 # Bidirectional LSTM model architecture
├── data/                        # Dataset directory
├── models/                      # Saved models and tokenizers
├── logs/                        # Training logs
├── plots/                       # Training visualization plots
├── train.py                     # Main training script
├── predict.py                   # Prediction and inference script
├── requirements.txt             # Python dependencies
└── README.md                    # This file
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd CSE4022_Project
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Dataset Setup

### Option 1: Download TinyStories Dataset (Recommended)

1. Go to one of these Kaggle datasets:
   - [TinyStories Narrative Classification](https://www.kaggle.com/datasets/thedevastator/tinystories-narrative-classification)
   - [Tiny Stories DS](https://www.kaggle.com/datasets/tomasbebra/tiny-stories-ds)

2. Download the dataset and extract the CSV file to the `data/` directory

3. Rename the file to `stories.csv` or note the filename for training

### Option 2: Use Sample Data

The code will automatically generate sample data if no dataset is found.

## Training the Model

### Basic Training

```bash
python train.py
```

### Advanced Training Options

```bash
python train.py --data_path data/stories.csv --epochs 100 --batch_size 64 --vocab_size 10000
```

### Training Parameters

- `--data_path`: Path to your dataset CSV file
- `--epochs`: Number of training epochs (default: 50)
- `--batch_size`: Batch size for training (default: 64)
- `--vocab_size`: Maximum vocabulary size (default: 5000)
- `--max_sequence_length`: Maximum sequence length (default: 30)

### Training Output

The training script will create:
- `models/best_bilstm_model.h5`: Best model weights
- `models/tokenizer.json`: Trained tokenizer
- `models/training_results.json`: Training metrics and configuration
- `plots/training_history.png`: Training visualization
- `logs/`: TensorBoard logs

## Making Predictions

### Command Line Prediction

```bash
# Single prediction
python predict.py --text "once upon a time" --num_words 5

# Show top predictions
python predict.py --text "the little girl" --show_top

# Interactive mode
python predict.py --interactive
```

### Prediction Parameters

- `--text`: Input text for prediction
- `--num_words`: Number of words to predict (default: 5)
- `--temperature`: Sampling temperature (default: 1.0)
  - Lower values (0.5): More conservative/predictable
  - Higher values (1.5): More creative/random
- `--top_k`: Top-k sampling (consider only top k predictions)
- `--top_p`: Top-p/nucleus sampling (consider top p% probability mass)
- `--interactive`: Run in interactive mode
- `--show_top`: Show top predictions instead of generating

### Example Usage

```bash
# Conservative prediction
python predict.py --text "the cat sat on" --temperature 0.5 --num_words 3

# Creative prediction
python predict.py --text "in the magical forest" --temperature 1.5 --num_words 5

# Top-k sampling
python predict.py --text "once upon a time" --top_k 10 --num_words 4
```

## Model Architecture

The Bidirectional LSTM model consists of:

1. **Embedding Layer**: Converts words to dense vectors
2. **Bidirectional LSTM Layers**: Two stacked Bi-LSTM layers with dropout
3. **Batch Normalization**: For training stability
4. **Global Max Pooling**: To get fixed-size output
5. **Dense Layers**: Fully connected layers with dropout
6. **Output Layer**: Softmax activation for word probability distribution

### Model Parameters

- Embedding dimension: 100
- LSTM units: 128 (first layer), 64 (second layer)
- Dropout rate: 0.3
- Regularization: L2 regularization on LSTM and Dense layers

## Evaluation Metrics

The model is evaluated using:

- **Accuracy**: Standard classification accuracy
- **Perplexity**: Measure of how well the model predicts the sequence
- **Top-k Accuracy**: Accuracy when considering top k predictions
- **Loss**: Sparse categorical crossentropy loss

## Training for Kaggle

To train on Kaggle with GPU:

1. Upload the Python files (`train.py`, `predict.py`, `src/` folder) to Kaggle
2. Download the TinyStories dataset in Kaggle
3. Create a new notebook and run:

```python
# Install requirements if needed
!pip install tensorflow matplotlib seaborn

# Run training
!python train.py --data_path /kaggle/input/dataset/stories.csv --epochs 100 --batch_size 128
```

4. Download the trained model files from the output

## Troubleshooting

### Common Issues

1. **Out of Memory**: Reduce batch size or sequence length
2. **Low Accuracy**: Increase model size, training epochs, or dataset size
3. **Overfitting**: Increase dropout rate or add more regularization

### Performance Tips

- Use GPU for faster training
- Increase vocabulary size for better word coverage
- Experiment with different sequence lengths
- Try different sampling strategies for prediction

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is for educational purposes as part of CSE4022 - Natural Language Processing course.
