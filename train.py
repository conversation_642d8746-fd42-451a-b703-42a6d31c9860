"""
Main training script for Next Word Prediction using Bidirectional LSTM.
This script handles the complete training pipeline with evaluation metrics.
"""

import os
import sys
import argparse
import json
import time
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report
import tensorflow as tf

# Add src to path
sys.path.append('src')

from data_preprocessing import DataPreprocessor
from model import BiLSTMNextWordPredictor


class TrainingPipeline:
    """
    Complete training pipeline for the next word prediction model.
    """
    
    def __init__(self, config):
        """
        Initialize the training pipeline.
        
        Args:
            config (dict): Configuration parameters
        """
        self.config = config
        self.preprocessor = None
        self.model = None
        self.history = None
        self.metrics = {}
        
    def setup_directories(self):
        """Create necessary directories."""
        directories = ['models', 'logs', 'plots', 'data']
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def prepare_data(self):
        """Prepare the training data."""
        print("=" * 50)
        print("PREPARING DATA")
        print("=" * 50)
        
        self.preprocessor = DataPreprocessor(
            max_sequence_length=self.config['max_sequence_length'],
            vocab_size=self.config['vocab_size']
        )
        
        # Load and prepare data
        X_train, X_test, y_train, y_test = self.preprocessor.prepare_data(
            file_path=self.config.get('data_path'),
            test_size=self.config['test_size'],
            random_state=self.config['random_state']
        )
        
        # Save tokenizer
        self.preprocessor.save_tokenizer(self.config['tokenizer_path'])
        
        # Update config with actual values
        self.config['vocab_size'] = self.preprocessor.total_words
        self.config['sequence_length'] = self.preprocessor.max_sequence_length
        
        print(f"Training samples: {len(X_train)}")
        print(f"Test samples: {len(X_test)}")
        print(f"Vocabulary size: {self.config['vocab_size']}")
        print(f"Sequence length: {self.config['sequence_length']}")
        
        return X_train, X_test, y_train, y_test
    
    def create_model(self):
        """Create and build the model."""
        print("\n" + "=" * 50)
        print("CREATING MODEL")
        print("=" * 50)
        
        self.model = BiLSTMNextWordPredictor(
            vocab_size=self.config['vocab_size'],
            embedding_dim=self.config['embedding_dim'],
            lstm_units=self.config['lstm_units'],
            sequence_length=self.config['sequence_length'],
            dropout_rate=self.config['dropout_rate']
        )
        
        # Build model
        model = self.model.build_model()
        
        print("Model created successfully!")
        return model
    
    def train_model(self, X_train, y_train, X_val, y_val):
        """Train the model."""
        print("\n" + "=" * 50)
        print("TRAINING MODEL")
        print("=" * 50)
        
        start_time = time.time()
        
        # Train model
        self.history = self.model.train(
            X_train, y_train, X_val, y_val,
            epochs=self.config['epochs'],
            batch_size=self.config['batch_size'],
            model_save_path=self.config['model_save_path']
        )
        
        training_time = time.time() - start_time
        self.metrics['training_time'] = training_time
        
        print(f"Training completed in {training_time:.2f} seconds")
        
        return self.history
    
    def evaluate_model(self, X_test, y_test):
        """Evaluate the model and calculate metrics."""
        print("\n" + "=" * 50)
        print("EVALUATING MODEL")
        print("=" * 50)
        
        # Basic evaluation
        test_metrics = self.model.evaluate(X_test, y_test)
        self.metrics.update(test_metrics)
        
        # Additional metrics
        y_pred = self.model.model.predict(X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        
        # Top-k accuracy
        for k in [1, 3, 5]:
            top_k_acc = tf.keras.metrics.sparse_top_k_categorical_accuracy(
                y_test, y_pred, k=k
            ).numpy().mean()
            self.metrics[f'top_{k}_accuracy'] = top_k_acc
            print(f"Top-{k} Accuracy: {top_k_acc:.4f}")
        
        return self.metrics
    
    def plot_training_history(self):
        """Plot training history."""
        if self.history is None:
            return
        
        print("\nCreating training plots...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Loss plot
        axes[0, 0].plot(self.history.history['loss'], label='Training Loss')
        axes[0, 0].plot(self.history.history['val_loss'], label='Validation Loss')
        axes[0, 0].set_title('Model Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # Accuracy plot
        axes[0, 1].plot(self.history.history['accuracy'], label='Training Accuracy')
        axes[0, 1].plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        axes[0, 1].set_title('Model Accuracy')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # Learning rate plot (if available)
        if 'lr' in self.history.history:
            axes[1, 0].plot(self.history.history['lr'])
            axes[1, 0].set_title('Learning Rate')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Learning Rate')
            axes[1, 0].set_yscale('log')
            axes[1, 0].grid(True)
        
        # Metrics summary
        metrics_text = f"""
        Final Training Loss: {self.history.history['loss'][-1]:.4f}
        Final Validation Loss: {self.history.history['val_loss'][-1]:.4f}
        Final Training Accuracy: {self.history.history['accuracy'][-1]:.4f}
        Final Validation Accuracy: {self.history.history['val_accuracy'][-1]:.4f}
        Best Validation Loss: {min(self.history.history['val_loss']):.4f}
        Best Validation Accuracy: {max(self.history.history['val_accuracy']):.4f}
        """
        
        axes[1, 1].text(0.1, 0.5, metrics_text, fontsize=10, 
                        verticalalignment='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Training Summary')
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plt.savefig('plots/training_history.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("Training plots saved to plots/training_history.png")
    
    def test_predictions(self):
        """Test the model with sample predictions."""
        print("\n" + "=" * 50)
        print("TESTING PREDICTIONS")
        print("=" * 50)
        
        test_sentences = [
            "once upon a time",
            "the little girl",
            "in the forest",
            "the cat sat",
            "it was a beautiful"
        ]
        
        print("Sample predictions:")
        for sentence in test_sentences:
            prediction = self.model.predict_next_word(
                self.preprocessor.tokenizer, 
                sentence, 
                num_words=3,
                temperature=0.8
            )
            print(f"Input: '{sentence}' -> Output: '{prediction}'")
    
    def save_results(self):
        """Save training results and configuration."""
        results = {
            'config': self.config,
            'metrics': self.metrics,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('models/training_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to models/training_results.json")
    
    def run_complete_pipeline(self):
        """Run the complete training pipeline."""
        print("Starting Next Word Prediction Training Pipeline")
        print(f"Timestamp: {datetime.now()}")
        print("Configuration:")
        for key, value in self.config.items():
            print(f"  {key}: {value}")
        
        # Setup
        self.setup_directories()
        
        # Prepare data
        X_train, X_test, y_train, y_test = self.prepare_data()
        
        # Create model
        self.create_model()
        
        # Train model
        self.train_model(X_train, y_train, X_test, y_test)
        
        # Evaluate model
        self.evaluate_model(X_test, y_test)
        
        # Create plots
        self.plot_training_history()
        
        # Test predictions
        self.test_predictions()
        
        # Save results
        self.save_results()
        
        print("\n" + "=" * 50)
        print("TRAINING PIPELINE COMPLETED!")
        print("=" * 50)
        print(f"Model saved to: {self.config['model_save_path']}")
        print(f"Tokenizer saved to: {self.config['tokenizer_path']}")
        print(f"Results saved to: models/training_results.json")


def main():
    """Main function to run training."""
    parser = argparse.ArgumentParser(description='Train Next Word Prediction Model')
    parser.add_argument('--data_path', type=str, default=None,
                       help='Path to dataset file')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=64,
                       help='Batch size for training')
    parser.add_argument('--vocab_size', type=int, default=5000,
                       help='Maximum vocabulary size')
    parser.add_argument('--max_sequence_length', type=int, default=30,
                       help='Maximum sequence length')
    
    args = parser.parse_args()
    
    # Configuration
    config = {
        'data_path': args.data_path,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'vocab_size': args.vocab_size,
        'max_sequence_length': args.max_sequence_length,
        'embedding_dim': 100,
        'lstm_units': 128,
        'dropout_rate': 0.3,
        'test_size': 0.2,
        'random_state': 42,
        'model_save_path': 'models/best_bilstm_model.h5',
        'tokenizer_path': 'models/tokenizer.json'
    }
    
    # Create and run pipeline
    pipeline = TrainingPipeline(config)
    pipeline.run_complete_pipeline()


if __name__ == "__main__":
    main()
