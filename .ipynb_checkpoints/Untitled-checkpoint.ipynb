{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f4f1eaab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Imports done\n"]}], "source": ["#imports\n", "from tensorflow.keras.preprocessing.sequence import pad_sequences\n", "from tensorflow.keras.layers import Embedding, LSTM, Dense, Dropout, Bidirectional\n", "from tensorflow.keras.preprocessing.text import Tokenizer\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras import models\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras import regularizers\n", "import tensorflow.keras.utils as ku \n", "import numpy as np\n", "print(\"Imports done\") #debug"]}, {"cell_type": "code", "execution_count": 3, "id": "e2c3c409", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["opening done\n", "corpus split\n", "tokenizer complete\n", "total words 2969\n"]}], "source": ["#pre processing\n", "tokenizer = Tokenizer()\n", "data = open('../../../../../Cooleg Extra/CSE4022/stories.txt', encoding='utf-8').read() #to fix\n", "print(\"opening done\") #debug\n", "corpus = data.lower().split(\"\\n\")\n", "print(\"corpus split\") #debug\n", "tokenizer.fit_on_texts(corpus)\n", "print(\"tokenizer complete\") #debug\n", "total_words = len(tokenizer.word_index) + 1\n", "print(\"total words\", total_words) #debug"]}, {"cell_type": "code", "execution_count": 3, "id": "5c638aad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input sequences generated\n", "input sequences padded\n", "predictors and label created\n"]}], "source": ["#creating input sequences\n", "input_sequences = []\n", "for line in corpus:\n", "    token_list = tokenizer.texts_to_sequences([line])[0]\n", "    for i in range(1, len(token_list)):\n", "        n_gram_sequence = token_list[:i+1]\n", "        input_sequences.append(n_gram_sequence)\n", "print(\"input sequences generated\") #debug\n", "# pad sequences \n", "max_sequence_len = max([len(x) for x in input_sequences])\n", "input_sequences = np.array(pad_sequences(input_sequences, maxlen=max_sequence_len, padding='pre'))\n", "print(\"input sequences padded\") #debug\n", "# create predictors and label\n", "predictors, label = input_sequences[:,:-1],input_sequences[:,-1]\n", "\n", "label = ku.to_categorical(label, num_classes=total_words)\n", "print(\"predictors and label created\")#debug"]}, {"cell_type": "code", "execution_count": 6, "id": "e849d5ac", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:Found untraced functions such as lstm_cell_7_layer_call_fn, lstm_cell_7_layer_call_and_return_conditional_losses, lstm_cell_5_layer_call_fn, lstm_cell_5_layer_call_and_return_conditional_losses, lstm_cell_6_layer_call_fn while saving (showing 5 of 15). These functions will not be directly callable after loading.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: saved_model/mainmodel\\assets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: saved_model/mainmodel\\assets\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x000001E1C01AD370> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x000001E1C01A9490> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x000001E1C01AA0D0> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n"]}, {"ename": "NameError", "evalue": "name 'history' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn [6], line 24\u001b[0m\n\u001b[0;32m     22\u001b[0m \u001b[38;5;66;03m#plot accuracy and loss\u001b[39;00m\n\u001b[0;32m     23\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmat<PERSON>lotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[1;32m---> 24\u001b[0m acc \u001b[38;5;241m=\u001b[39m \u001b[43mhistory\u001b[49m\u001b[38;5;241m.\u001b[39mhistory[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124maccuracy\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     25\u001b[0m loss \u001b[38;5;241m=\u001b[39m history\u001b[38;5;241m.\u001b[39mhistory[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mloss\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     26\u001b[0m epochs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(acc))\n", "\u001b[1;31mNameError\u001b[0m: name 'history' is not defined"]}], "source": ["#creating a sequential model from scratch\n", "\n", "'''model = Sequential()\n", "model.add(Embedding(total_words, 100, input_length=max_sequence_len-1))\n", "model.add(Bidirectional(LSTM(150, return_sequences = True)))\n", "model.add(Dropout(0.2))\n", "model.add(LSTM(100))\n", "model.add(Den<PERSON>(total_words/2, activation='relu', kernel_regularizer=regularizers.l2(0.01)))\n", "model.add(Dense(total_words, activation='softmax'))\n", "model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])\n", "print(model.summary())\n", "'''\n", "\n", "#loading presaved model\n", "new_model = models.load_model('saved_model/mainmodel')\n", "#training for x epochs\n", "\n", "#history = new_model.fit(predictors, label, epochs=1, verbose=1)\n", "\n", "#saving the model for future use\n", "new_model.save('saved_model/mainmodel')\n", "#plot accuracy and loss\n", "import matplotlib.pyplot as plt\n", "acc = history.history['accuracy']\n", "loss = history.history['loss']\n", "epochs = range(len(acc))\n", "plt.plot(epochs, acc, 'b', label='Training accuracy')\n", "plt.title('Training accuracy')\n", "plt.figure()\n", "plt.plot(epochs, loss, 'b', label='Training Loss')\n", "plt.title('Training loss')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "id": "2a9e6876", "metadata": {"scrolled": true}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "Interrupted by user", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn [4], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m#testing\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m seed_text \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43minput\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mInput String: \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m next_words \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(\u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNumber of words: \u001b[39m\u001b[38;5;124m\"\u001b[39m))\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(next_words):\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\ipykernel\\kernelbase.py:1177\u001b[0m, in \u001b[0;36mKernel.raw_input\u001b[1;34m(self, prompt)\u001b[0m\n\u001b[0;32m   1173\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_allow_stdin:\n\u001b[0;32m   1174\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m StdinNotImplementedError(\n\u001b[0;32m   1175\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraw_input was called, but this frontend does not support input requests.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1176\u001b[0m     )\n\u001b[1;32m-> 1177\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_input_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   1178\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1179\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_parent_ident\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mshell\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1180\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_parent\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mshell\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1181\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1182\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\ipykernel\\kernelbase.py:1219\u001b[0m, in \u001b[0;36mKernel._input_request\u001b[1;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[0;32m   1216\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m   1217\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[0;32m   1218\u001b[0m     \u001b[38;5;66;03m# re-raise KeyboardInterrupt, to truncate traceback\u001b[39;00m\n\u001b[1;32m-> 1219\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInterrupted by user\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28m<PERSON><PERSON>\u001b[39m\n\u001b[0;32m   1220\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[0;32m   1221\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlog\u001b[38;5;241m.\u001b[39mwarning(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid Message:\u001b[39m\u001b[38;5;124m\"\u001b[39m, exc_info\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}], "source": ["#testing\n", "new_model = models.load_model('saved_model/mainmodel')\n", "seed_text = input(\"Input String: \")\n", "next_words = int(input(\"Number of words: \"))\n", "for _ in range(next_words):\n", "    token_list = tokenizer.texts_to_sequences([seed_text])[0]\n", "    token_list = pad_sequences([token_list], maxlen=max_sequence_len-1, padding='pre')\n", "    #predicted = model.predict_classes(token_list, verbose=0)\n", "    predicted = new_model.predict(token_list)\n", "    #predicted = new_model(token_list)\n", "    output_word = \"\"\n", "    for word, index in tokenizer.word_index.items():\n", "        if index == np.argmax(predicted):\n", "            output_word = word\n", "            break\n", "    seed_text += \" \" + output_word\n", "print(seed_text)"]}, {"cell_type": "code", "execution_count": 5, "id": "8d092fbd", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:Found untraced functions such as lstm_cell_3_layer_call_fn, lstm_cell_3_layer_call_and_return_conditional_losses, lstm_cell_1_layer_call_fn, lstm_cell_1_layer_call_and_return_conditional_losses, lstm_cell_2_layer_call_fn while saving (showing 5 of 15). These functions will not be directly callable after loading.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: saved_model/mainmodel\\assets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: saved_model/mainmodel\\assets\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x0000022BFACC93A0> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x0000022BFAB9F4C0> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n", "WARNING:absl:<keras.layers.recurrent.LSTMCell object at 0x0000022BFACC2100> has the same name 'LSTMCell' as a built-in Keras object. Consider renaming <class 'keras.layers.recurrent.LSTMCell'> to avoid naming conflicts when loading with `tf.keras.models.load_model`. If renaming is not possible, pass the object in the `custom_objects` parameter of the load function.\n"]}], "source": ["#saving the model for future use\n", "new_model.save('saved_model/mainmodel')"]}, {"cell_type": "code", "execution_count": 3, "id": "cce36297", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " embedding (Embedding)       (None, 87, 100)           296900    \n", "                                                                 \n", " bidirectional (Bidirectiona  (None, 87, 300)          301200    \n", " l)                                                              \n", "                                                                 \n", " dropout (Dropout)           (None, 87, 300)           0         \n", "                                                                 \n", " lstm_1 (LSTM)               (None, 100)               160400    \n", "                                                                 \n", " dense (<PERSON><PERSON>)               (None, 1484)              149884    \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, 2969)              4408965   \n", "                                                                 \n", "=================================================================\n", "Total params: 5,317,349\n", "Trainable params: 5,317,349\n", "Non-trainable params: 0\n", "_________________________________________________________________\n", "None\n"]}], "source": ["new_model = models.load_model('saved_model/mainmodel')\n", "print(new_model.summary())"]}, {"cell_type": "code", "execution_count": null, "id": "ca356d7e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 5}