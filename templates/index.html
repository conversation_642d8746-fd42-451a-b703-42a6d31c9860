<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>CSE4022 Project</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-Zenh87qX5JnK2Jl0vWa8Ck2rdkQ2Bzep5IDxbcnCeuOxjzrPF/et3URy9Bv1WTRi" crossorigin="anonymous">
    <script src="https://code.jquery.com/jquery-3.6.1.js" integrity="sha256-3zlB5s2uwoUzrXK3BT7AX3FyvojsraNFxCc2vC/7pNI=" crossorigin="anonymous"></script>
    <script>
        $(document).ready( () => {
            $("input[name='WordCount']").change(() => {
                if ($("input[name='WordCount']:checked").val() == "single") {
                    $("#wordNumber").prop("disabled", true)
                    $("#wordNumberLabel").html("Number of Words: 1")
                }else{
                    $("#wordNumber").prop("disabled", false)
                    $("#wordNumberLabel").html("Number of Words: " + $("#wordNumber").val())
                }
            })
            $("#wordNumber").change(() => {
                // console.log(typeof($("#wordNumber").val()))
                $("#wordNumberLabel").html("Number of Words: " + $("#wordNumber").val())
            })
        })
    </script>
  </head>
  <body>
    <div class="container">
        <header class="d-flex flex-wrap justify-content-center py-3 mb-4 border-bottom">
            <a href="/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none">
              <span class="fs-4">CSE4022 Project</span>

            </a>
          </header>
    </div>

      <div class="container col-4">
        <ul class="list-group">
            <li class="list-group-item">Team Members:</li>
            <li class="list-group-item">Syed Jawwad Lateef 20BCT0034</li>
            <li class="list-group-item">B. Venkata Mounish Reddy - 20BDS0149</li>
            <li class="list-group-item">Vatsal Khushalani - 20BDS0144</li>
        </ul>
      </div>
      <br/>

      <div class="container border border-2 border-primary">
        <form action="http://localhost:5000/result" method = "POST">
            <div class="row p-0"><label for="inputText" class="form-label">Text Input</label></div>
            <div class="row p-2"><textarea class="formcontrol" name="inputText" id="inputText" cols="30" rows="10">{{input}}</textarea></div>
            <div class="row justify-content-center">
                <span class="col-4">
                    <input type="radio" name="WordCount" id="single" class="form-check-input" value = "single">
                    <label for="SingleRadio" class="form-label">Single Word</label>
                </span>
                <span class="col-4">
                    <input type="radio" name="WordCount" id="multiple" class="form-check-input" value = "multiple">
                    <label for="MultipleRadio" class="form-label">Multiple Words</label>
                </span>
            </div>
            <div class="row justify-content-center mx-2">
                <label for="wordNumber" class="form-label" id = "wordNumberLabel">Number of Words: 1</label>
                <input type="range" class="form-range" id="wordNumber" name="wordNumber">
            </div>
            <div class="row justify-content-center">
                <div class="col-2"><input type="submit" value="submit"></div>
                
            </div>
            
            
        </form>
      </div>
      <br/>

      <div class="container border border-2 border-secondary">
        <div class="row justify-content-center">
            <label for="outputArea" class="form-label">Output Text</label>
            <textarea class="formcontrol" name="outputArea" id="outputArea" cols="30" rows="10" disabled>{{output}}</textarea>
        </div>
      </div>
    


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-OERcA2EqjJCMA+/3y+gxIOqMEjwtxJY7qPCqsdltbNJuaOe923+mo//f6V8Qbsw3" crossorigin="anonymous"></script>
  </body>
</html>