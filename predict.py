"""
Inference script for Next Word Prediction using trained Bidirectional LSTM model.
This script loads a trained model and provides prediction capabilities.
"""

import os
import sys
import argparse
import json
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.text import tokenizer_from_json
from tensorflow.keras.preprocessing.sequence import pad_sequences

# Add src to path
sys.path.append('src')

from model import BiLSTMNextWordPredictor


class NextWordPredictor:
    """
    Class for making predictions with a trained next word prediction model.
    """
    
    def __init__(self, model_path="models/best_bilstm_model.h5", 
                 tokenizer_path="models/tokenizer.json"):
        """
        Initialize the predictor.
        
        Args:
            model_path (str): Path to the trained model
            tokenizer_path (str): Path to the saved tokenizer
        """
        self.model_path = model_path
        self.tokenizer_path = tokenizer_path
        self.model = None
        self.tokenizer = None
        self.sequence_length = None
        self.vocab_size = None
        
    def load_model_and_tokenizer(self):
        """Load the trained model and tokenizer."""
        # Load model
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
        
        print(f"Loading model from: {self.model_path}")
        self.model = tf.keras.models.load_model(self.model_path)
        
        # Get model input shape
        input_shape = self.model.input_shape
        self.sequence_length = input_shape[1] + 1  # Add 1 for the target word
        
        # Load tokenizer
        if not os.path.exists(self.tokenizer_path):
            raise FileNotFoundError(f"Tokenizer file not found: {self.tokenizer_path}")
        
        print(f"Loading tokenizer from: {self.tokenizer_path}")
        with open(self.tokenizer_path, 'r', encoding='utf-8') as f:
            tokenizer_json = f.read()
        
        self.tokenizer = tokenizer_from_json(tokenizer_json)
        self.vocab_size = len(self.tokenizer.word_index) + 1
        
        print(f"Model loaded successfully!")
        print(f"Vocabulary size: {self.vocab_size}")
        print(f"Sequence length: {self.sequence_length}")
        
    def predict_next_word(self, seed_text, num_words=1, temperature=1.0, 
                         top_k=None, top_p=None):
        """
        Predict next word(s) given a seed text.
        
        Args:
            seed_text (str): Input text
            num_words (int): Number of words to predict
            temperature (float): Sampling temperature (higher = more random)
            top_k (int): Consider only top k predictions
            top_p (float): Nucleus sampling threshold
            
        Returns:
            str: Generated text
        """
        if self.model is None or self.tokenizer is None:
            self.load_model_and_tokenizer()
        
        generated_text = seed_text.lower().strip()
        
        for i in range(num_words):
            # Tokenize input
            token_list = self.tokenizer.texts_to_sequences([generated_text])[0]
            
            # Pad sequence
            token_list = pad_sequences(
                [token_list], 
                maxlen=self.sequence_length - 1, 
                padding='pre'
            )
            
            # Predict
            predictions = self.model.predict(token_list, verbose=0)[0]
            
            # Apply temperature
            if temperature != 1.0:
                predictions = predictions / temperature
                predictions = tf.nn.softmax(predictions).numpy()
            
            # Apply top-k filtering
            if top_k is not None:
                top_k_indices = np.argpartition(predictions, -top_k)[-top_k:]
                filtered_predictions = np.zeros_like(predictions)
                filtered_predictions[top_k_indices] = predictions[top_k_indices]
                predictions = filtered_predictions / np.sum(filtered_predictions)
            
            # Apply top-p (nucleus) sampling
            if top_p is not None:
                sorted_indices = np.argsort(predictions)[::-1]
                cumulative_probs = np.cumsum(predictions[sorted_indices])
                cutoff_index = np.searchsorted(cumulative_probs, top_p)
                
                filtered_predictions = np.zeros_like(predictions)
                filtered_predictions[sorted_indices[:cutoff_index+1]] = \
                    predictions[sorted_indices[:cutoff_index+1]]
                predictions = filtered_predictions / np.sum(filtered_predictions)
            
            # Sample from predictions
            if np.sum(predictions) == 0:
                predicted_id = np.argmax(predictions)
            else:
                predicted_id = np.random.choice(len(predictions), p=predictions)
            
            # Convert back to word
            output_word = ""
            for word, index in self.tokenizer.word_index.items():
                if index == predicted_id:
                    output_word = word
                    break
            
            if output_word and output_word != '<OOV>':
                generated_text += " " + output_word
            else:
                break  # Stop if we can't find a valid word
        
        return generated_text
    
    def get_top_predictions(self, seed_text, top_n=5):
        """
        Get top N word predictions for the next word.
        
        Args:
            seed_text (str): Input text
            top_n (int): Number of top predictions to return
            
        Returns:
            list: List of (word, probability) tuples
        """
        if self.model is None or self.tokenizer is None:
            self.load_model_and_tokenizer()
        
        # Tokenize input
        token_list = self.tokenizer.texts_to_sequences([seed_text.lower()])[0]
        
        # Pad sequence
        token_list = pad_sequences(
            [token_list], 
            maxlen=self.sequence_length - 1, 
            padding='pre'
        )
        
        # Predict
        predictions = self.model.predict(token_list, verbose=0)[0]
        
        # Get top predictions
        top_indices = np.argsort(predictions)[-top_n:][::-1]
        
        results = []
        for idx in top_indices:
            for word, word_idx in self.tokenizer.word_index.items():
                if word_idx == idx:
                    results.append((word, predictions[idx]))
                    break
        
        return results
    
    def interactive_prediction(self):
        """Interactive prediction mode."""
        print("=" * 50)
        print("INTERACTIVE NEXT WORD PREDICTION")
        print("=" * 50)
        print("Enter text and get next word predictions!")
        print("Commands:")
        print("  'quit' or 'exit' - Exit the program")
        print("  'help' - Show this help message")
        print("  'top <text>' - Show top 5 predictions for next word")
        print("=" * 50)
        
        while True:
            try:
                user_input = input("\nEnter text: ").strip()
                
                if user_input.lower() in ['quit', 'exit']:
                    print("Goodbye!")
                    break
                
                if user_input.lower() == 'help':
                    print("Commands:")
                    print("  'quit' or 'exit' - Exit the program")
                    print("  'help' - Show this help message")
                    print("  'top <text>' - Show top 5 predictions for next word")
                    continue
                
                if user_input.lower().startswith('top '):
                    text = user_input[4:]
                    if text:
                        predictions = self.get_top_predictions(text, top_n=5)
                        print(f"\nTop 5 predictions for '{text}':")
                        for i, (word, prob) in enumerate(predictions, 1):
                            print(f"  {i}. {word} (probability: {prob:.4f})")
                    continue
                
                if not user_input:
                    continue
                
                # Generate predictions with different settings
                print(f"\nInput: '{user_input}'")
                print("-" * 30)
                
                # Conservative prediction
                conservative = self.predict_next_word(
                    user_input, num_words=3, temperature=0.5
                )
                print(f"Conservative: {conservative}")
                
                # Balanced prediction
                balanced = self.predict_next_word(
                    user_input, num_words=3, temperature=1.0
                )
                print(f"Balanced:     {balanced}")
                
                # Creative prediction
                creative = self.predict_next_word(
                    user_input, num_words=3, temperature=1.5
                )
                print(f"Creative:     {creative}")
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error: {e}")


def main():
    """Main function for prediction script."""
    parser = argparse.ArgumentParser(description='Next Word Prediction')
    parser.add_argument('--model_path', type=str, 
                       default='models/best_bilstm_model.h5',
                       help='Path to trained model')
    parser.add_argument('--tokenizer_path', type=str, 
                       default='models/tokenizer.json',
                       help='Path to saved tokenizer')
    parser.add_argument('--text', type=str, default=None,
                       help='Input text for prediction')
    parser.add_argument('--num_words', type=int, default=5,
                       help='Number of words to predict')
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='Sampling temperature')
    parser.add_argument('--top_k', type=int, default=None,
                       help='Top-k sampling')
    parser.add_argument('--top_p', type=float, default=None,
                       help='Top-p (nucleus) sampling')
    parser.add_argument('--interactive', action='store_true',
                       help='Run in interactive mode')
    parser.add_argument('--show_top', action='store_true',
                       help='Show top predictions instead of generating')
    
    args = parser.parse_args()
    
    # Create predictor
    predictor = NextWordPredictor(args.model_path, args.tokenizer_path)
    
    try:
        if args.interactive:
            predictor.interactive_prediction()
        elif args.text:
            if args.show_top:
                predictions = predictor.get_top_predictions(args.text, top_n=10)
                print(f"Top predictions for '{args.text}':")
                for i, (word, prob) in enumerate(predictions, 1):
                    print(f"  {i:2d}. {word:<15} (probability: {prob:.4f})")
            else:
                result = predictor.predict_next_word(
                    args.text, 
                    num_words=args.num_words,
                    temperature=args.temperature,
                    top_k=args.top_k,
                    top_p=args.top_p
                )
                print(f"Input:  {args.text}")
                print(f"Output: {result}")
        else:
            print("Please provide --text for prediction or use --interactive mode")
            print("Use --help for more options")
    
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Make sure you have trained a model first using train.py")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
