"""
Example usage script for Next Word Prediction model.
This script demonstrates how to use the training and prediction modules.
"""

import os
import sys

# Add src to path
sys.path.append('src')

from data_preprocessing import DataPreprocessor
from model import BiLSTMNextWordPredictor


def quick_training_example():
    """
    Quick training example with sample data.
    """
    print("=" * 60)
    print("QUICK TRAINING EXAMPLE")
    print("=" * 60)
    
    # Create directories
    os.makedirs('models', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    
    # Initialize preprocessor
    preprocessor = DataPreprocessor(max_sequence_length=20, vocab_size=2000)
    
    # Prepare data (will use sample data if no dataset found)
    print("Preparing data...")
    X_train, X_test, y_train, y_test = preprocessor.prepare_data()
    
    # Save tokenizer
    preprocessor.save_tokenizer()
    
    # Create model
    print("\nCreating model...")
    model = BiLSTMNextWordPredictor(
        vocab_size=preprocessor.total_words,
        sequence_length=preprocessor.max_sequence_length,
        embedding_dim=50,  # Smaller for quick training
        lstm_units=64,     # Smaller for quick training
        dropout_rate=0.2
    )
    
    # Build model
    model.build_model()
    model.get_model_summary()
    
    # Quick training (just a few epochs for demonstration)
    print("\nStarting quick training...")
    history = model.train(
        X_train, y_train, X_test, y_test,
        epochs=5,  # Very few epochs for quick demo
        batch_size=32,
        model_save_path='models/quick_demo_model.h5'
    )
    
    # Evaluate
    print("\nEvaluating model...")
    metrics = model.evaluate(X_test, y_test)
    
    # Test predictions
    print("\nTesting predictions...")
    test_sentences = [
        "once upon a time",
        "the little girl",
        "in the forest"
    ]
    
    for sentence in test_sentences:
        prediction = model.predict_next_word(
            preprocessor.tokenizer, 
            sentence, 
            num_words=3
        )
        print(f"'{sentence}' -> '{prediction}'")
    
    print("\nQuick training completed!")
    return model, preprocessor


def prediction_example():
    """
    Example of using a trained model for predictions.
    """
    print("\n" + "=" * 60)
    print("PREDICTION EXAMPLE")
    print("=" * 60)
    
    # Check if we have a trained model
    if not os.path.exists('models/quick_demo_model.h5'):
        print("No trained model found. Running quick training first...")
        model, preprocessor = quick_training_example()
    else:
        print("Loading existing model...")
        # Load model and tokenizer
        model = BiLSTMNextWordPredictor(vocab_size=1000, sequence_length=20)
        model.load_model('models/quick_demo_model.h5')
        
        preprocessor = DataPreprocessor()
        preprocessor.load_tokenizer()
    
    # Example predictions with different settings
    test_text = "the cat sat on"
    
    print(f"\nInput text: '{test_text}'")
    print("-" * 40)
    
    # Different temperature settings
    for temp in [0.5, 1.0, 1.5]:
        prediction = model.predict_next_word(
            preprocessor.tokenizer,
            test_text,
            num_words=4,
            temperature=temp
        )
        print(f"Temperature {temp}: {prediction}")


def main():
    """
    Main function to run examples.
    """
    print("Next Word Prediction - Example Usage")
    print("This script demonstrates basic usage of the model.")
    
    try:
        # Run quick training example
        quick_training_example()
        
        # Run prediction example
        prediction_example()
        
        print("\n" + "=" * 60)
        print("EXAMPLES COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nNext steps:")
        print("1. Download a larger dataset (TinyStories) for better results")
        print("2. Run full training: python train.py --epochs 50")
        print("3. Use predictions: python predict.py --interactive")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have installed all requirements: pip install -r requirements.txt")


if __name__ == "__main__":
    main()
